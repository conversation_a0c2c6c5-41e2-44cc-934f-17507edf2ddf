/**
 * 七牛云数据上传脚本
 * 将本地数据批量上传到七牛云对象存储
 */

const qiniu = require("qiniu");
const fs = require("fs");
const path = require("path");

// 七牛云配置
const qiniuConfig = {
  // 请填入你的七牛云配置
  ACCESS_KEY: "oZsUmpCeCCSL24rbYsH1g3YW6hLMojhEGp4Ua0cb", // 替换为你的AccessKey
  SECRET_KEY: "UhTlav9aMpjGfrLK3kDLGCnvwe8FfN9KNQ1bNmHy", // 替换为你的SecretKey
  BUCKET: "naixi-ai-tools", // 存储空间名称
  DOMAIN: "naixi.qiniu.logitnote.com", // CDN域名
};

// 数据路径配置
const DATA_CONFIG = {
  DATA_VERSION: "20250427",
  LOCAL_BASE_PATH: path.join(__dirname, "../data/数据20250427"),
  QINIU_BASE_PATH: "数据20250427", // 七牛云中的基础路径，直接上传到根目录
};

// 初始化七牛云配置
const mac = new qiniu.auth.digest.Mac(
  qiniuConfig.ACCESS_KEY,
  qiniuConfig.SECRET_KEY
);
const config = new qiniu.conf.Config();
config.zone = qiniu.zone.Zone_z0; // 华东区，根据你的存储区域调整

// 上传管理器
const formUploader = new qiniu.form_up.FormUploader(config);
const putExtra = new qiniu.form_up.PutExtra();

/**
 * 生成上传凭证
 */
function generateUploadToken(key) {
  const putPolicy = new qiniu.rs.PutPolicy({
    scope: `${qiniuConfig.BUCKET}:${key}`,
    expires: 7200, // 2小时有效期
  });
  return putPolicy.uploadToken(mac);
}

/**
 * 上传单个文件到七牛云
 */
function uploadFile(localPath, qiniuKey) {
  return new Promise((resolve, reject) => {
    const uploadToken = generateUploadToken(qiniuKey);

    console.log(`📤 开始上传: ${qiniuKey}`);

    formUploader.putFile(
      uploadToken,
      qiniuKey,
      localPath,
      putExtra,
      (respErr, respBody, respInfo) => {
        if (respErr) {
          console.error(`❌ 上传失败: ${qiniuKey}`, respErr);
          reject(respErr);
          return;
        }

        if (respInfo.statusCode === 200) {
          const fileUrl = `https://${qiniuConfig.DOMAIN}/${qiniuKey}`;
          console.log(`✅ 上传成功: ${qiniuKey} -> ${fileUrl}`);
          resolve({
            key: qiniuKey,
            url: fileUrl,
            hash: respBody.hash,
          });
        } else {
          console.error(
            `❌ 上传失败: ${qiniuKey}`,
            respInfo.statusCode,
            respBody
          );
          reject(new Error(`上传失败: ${respInfo.statusCode}`));
        }
      }
    );
  });
}

/**
 * 批量上传图片文件
 */
async function uploadImages() {
  console.log("🖼️  开始上传图片文件...");

  const imagesPath = path.join(DATA_CONFIG.LOCAL_BASE_PATH, "图片");
  const categories = fs
    .readdirSync(imagesPath, { withFileTypes: true })
    .filter((dirent) => dirent.isDirectory())
    .map((dirent) => dirent.name);

  const uploadResults = [];
  let totalFiles = 0;
  let successCount = 0;

  for (const category of categories) {
    console.log(`\n📁 处理分类: ${category}`);

    const categoryPath = path.join(imagesPath, category);
    const files = fs
      .readdirSync(categoryPath)
      .filter((file) => file.toLowerCase().endsWith(".png"));

    totalFiles += files.length;
    console.log(`   找到 ${files.length} 个图片文件`);

    for (const file of files) {
      const localFilePath = path.join(categoryPath, file);
      const qiniuKey = `${DATA_CONFIG.QINIU_BASE_PATH}/images/${category}/${file}`;

      try {
        const result = await uploadFile(localFilePath, qiniuKey);
        uploadResults.push({
          category,
          file,
          ...result,
        });
        successCount++;

        // 添加小延迟，避免请求过快
        await new Promise((resolve) => setTimeout(resolve, 100));
      } catch (error) {
        console.error(`❌ ${category}/${file} 上传失败:`, error.message);
        uploadResults.push({
          category,
          file,
          error: error.message,
        });
      }
    }
  }

  console.log(`\n🖼️  图片上传完成: ${successCount}/${totalFiles} 成功`);
  return uploadResults;
}

/**
 * 上传JSON数据文件
 */
async function uploadJsonData() {
  console.log("\n📄 开始上传JSON数据文件...");

  const jsonFileName = `tools_new_${DATA_CONFIG.DATA_VERSION}.json`;
  const localJsonPath = path.join(DATA_CONFIG.LOCAL_BASE_PATH, jsonFileName);
  const qiniuKey = `${DATA_CONFIG.QINIU_BASE_PATH}/${jsonFileName}`;

  try {
    const result = await uploadFile(localJsonPath, qiniuKey);
    console.log("✅ JSON数据文件上传成功");
    return result;
  } catch (error) {
    console.error("❌ JSON数据文件上传失败:", error);
    throw error;
  }
}

/**
 * 生成新的JSON文件（更新图片路径为七牛云CDN地址）
 */
async function generateQiniuJson(imageUploadResults) {
  console.log("\n🔄 生成七牛云版本的JSON文件...");

  const jsonFileName = `tools_new_${DATA_CONFIG.DATA_VERSION}.json`;
  const localJsonPath = path.join(DATA_CONFIG.LOCAL_BASE_PATH, jsonFileName);
  const originalData = JSON.parse(fs.readFileSync(localJsonPath, "utf8"));

  // 创建图片URL映射
  const imageUrlMap = {};
  imageUploadResults.forEach((result) => {
    if (!result.error) {
      const localPath = `图片/${result.category}/${result.file}`;
      imageUrlMap[localPath] = result.url;
    }
  });

  // 更新工具数据中的图片路径
  const updatedTools = originalData.tools.map((tool) => {
    if (tool.localIcon && imageUrlMap[tool.localIcon]) {
      return {
        ...tool,
        icon: imageUrlMap[tool.localIcon],
        qiniuIcon: imageUrlMap[tool.localIcon], // 保留七牛云地址
        originalIcon: tool.icon, // 保留原始云存储地址
      };
    }
    return tool;
  });

  const updatedData = {
    ...originalData,
    tools: updatedTools,
  };

  // 保存新的JSON文件
  const qiniuJsonPath = path.join(
    __dirname,
    `tools_qiniu_${DATA_CONFIG.DATA_VERSION}.json`
  );
  fs.writeFileSync(qiniuJsonPath, JSON.stringify(updatedData, null, 2), "utf8");
  console.log(`✅ 七牛云版本JSON文件已生成: ${qiniuJsonPath}`);

  // 上传新的JSON文件
  const qiniuJsonKey = `${DATA_CONFIG.QINIU_BASE_PATH}/tools_qiniu_${DATA_CONFIG.DATA_VERSION}.json`;
  const uploadResult = await uploadFile(qiniuJsonPath, qiniuJsonKey);

  return {
    localPath: qiniuJsonPath,
    uploadResult,
    imageCount: Object.keys(imageUrlMap).length,
  };
}

/**
 * 主上传流程
 */
async function main() {
  console.log("🚀 开始上传数据到七牛云...");
  console.log(`📊 配置信息:`);
  console.log(`   存储空间: ${qiniuConfig.BUCKET}`);
  console.log(`   CDN域名: ${qiniuConfig.DOMAIN}`);
  console.log(`   数据版本: ${DATA_CONFIG.DATA_VERSION}`);
  console.log(`   本地路径: ${DATA_CONFIG.LOCAL_BASE_PATH}`);
  console.log(`   七牛云路径: ${DATA_CONFIG.QINIU_BASE_PATH}`);

  try {
    // 1. 上传图片文件
    const imageResults = await uploadImages();

    // 2. 生成并上传七牛云版本的JSON文件
    const jsonResult = await generateQiniuJson(imageResults);

    // 3. 输出上传总结
    console.log("\n🎉 上传完成总结:");
    console.log(
      `   图片文件: ${imageResults.filter((r) => !r.error).length} 个成功`
    );
    console.log(`   JSON文件: 1 个`);
    console.log(
      `   CDN基础地址: https://${qiniuConfig.DOMAIN}/${DATA_CONFIG.QINIU_BASE_PATH}/`
    );
    console.log(`   JSON访问地址: ${jsonResult.uploadResult.url}`);

    // 4. 生成配置建议
    console.log("\n⚙️  配置更新建议:");
    console.log("在 config.js 中添加:");
    console.log(`QINIU: {
  DOMAIN: "${qiniuConfig.DOMAIN}",
  BASE_PATH: "${DATA_CONFIG.QINIU_BASE_PATH}",
  TOOLS_JSON_PATH: "${DATA_CONFIG.QINIU_BASE_PATH}/tools_qiniu_${DATA_CONFIG.DATA_VERSION}.json",
  IMAGES_BASE_PATH: "${DATA_CONFIG.QINIU_BASE_PATH}/images"
}`);
  } catch (error) {
    console.error("❌ 上传过程中出现错误:", error);
    process.exit(1);
  }
}

// 检查配置
function checkConfig() {
  const requiredKeys = ["ACCESS_KEY", "SECRET_KEY", "BUCKET", "DOMAIN"];
  const missingKeys = requiredKeys.filter(
    (key) => !qiniuConfig[key] || qiniuConfig[key].startsWith("your_")
  );

  if (missingKeys.length > 0) {
    console.error("❌ 请先配置七牛云参数:");
    missingKeys.forEach((key) => {
      console.error(`   ${key}: ${qiniuConfig[key]}`);
    });
    console.log("\n📖 获取配置信息:");
    console.log("   1. 登录七牛云控制台: https://portal.qiniu.com/");
    console.log("   2. 创建存储空间（对象存储）");
    console.log("   3. 获取AccessKey和SecretKey（密钥管理）");
    console.log("   4. 配置CDN加速域名");
    process.exit(1);
  }
}

// 运行脚本
if (require.main === module) {
  checkConfig();
  main().catch(console.error);
}

module.exports = {
  uploadFile,
  uploadImages,
  uploadJsonData,
  generateQiniuJson,
};
