/**
 * 数据管理器工厂
 * 根据配置决定使用本地还是云端数据管理器
 */

const config = require("../config/config");
const cloudDataManager = require("./cloudFunctionManager");
const localDataManager = require("./localDataManager");

// 根据配置选择数据管理器
function getDataManager() {
  const dataSourceType = config.DATA_SOURCE.TYPE;

  console.log(
    `数据管理器工厂: 使用${dataSourceType === "local" ? "本地" : "云端"}数据源`
  );

  if (dataSourceType === "local") {
    return localDataManager;
  } else {
    return cloudDataManager;
  }
}

// 获取数据源类型
function getDataSourceType() {
  return config.DATA_SOURCE.TYPE;
}

// 获取当前环境ID
function getCloudEnvId() {
  return config.DATA_SOURCE.CLOUD.ENV_ID;
}

module.exports = {
  getDataManager,
  getDataSourceType,
  getCloudEnvId,
};
